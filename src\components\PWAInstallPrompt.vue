<template>
  <div v-if="showInstallPrompt" class="pwa-install-prompt">
    <div class="prompt-content">
      <div class="prompt-header">
        <img src="/ic_launcher1.png" alt="App Icon" class="app-icon">
        <div class="app-info">
          <h3>安装 {{ appName }}</h3>
          <p>获得更好的体验，像原生应用一样使用</p>
        </div>
        <button @click="dismissPrompt" class="close-btn">×</button>
      </div>
      
      <div class="prompt-actions">
        <button @click="installApp" class="install-btn" :disabled="installing">
          {{ installing ? '安装中...' : '立即安装' }}
        </button>
        <button @click="dismissPrompt" class="cancel-btn">稍后再说</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

const showInstallPrompt = ref(false)
const installing = ref(false)
const appName = ref('cagadoslot')
let deferredPrompt: BeforeInstallPromptEvent | null = null

// 检测是否为Android设备
const isAndroid = () => {
  return /Android/i.test(navigator.userAgent)
}

// 检测是否为Chrome浏览器
const isChrome = () => {
  return /Chrome/i.test(navigator.userAgent) && !/Edge/i.test(navigator.userAgent)
}

// 检测是否已经安装
const isAppInstalled = () => {
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone === true
}

// 检测是否支持PWA安装
const isPWASupported = () => {
  return 'serviceWorker' in navigator && 'BeforeInstallPromptEvent' in window
}

// 处理beforeinstallprompt事件
const handleBeforeInstallPrompt = (e: Event) => {
  e.preventDefault()
  deferredPrompt = e as BeforeInstallPromptEvent
  
  // 只在Android Chrome上显示安装提示
  if (isAndroid() && isChrome() && !isAppInstalled()) {
    showInstallPrompt.value = true
  }
}

// 安装应用
const installApp = async () => {
  if (!deferredPrompt) return
  
  installing.value = true
  
  try {
    // 显示安装提示
    await deferredPrompt.prompt()
    
    // 等待用户选择
    const { outcome } = await deferredPrompt.userChoice
    
    if (outcome === 'accepted') {
      console.log('用户接受了安装提示')
      // 可以在这里添加统计代码
      trackInstallEvent('accepted')
    } else {
      console.log('用户拒绝了安装提示')
      trackInstallEvent('dismissed')
    }
    
    showInstallPrompt.value = false
    deferredPrompt = null
  } catch (error) {
    console.error('安装过程中出错:', error)
  } finally {
    installing.value = false
  }
}

// 关闭提示
const dismissPrompt = () => {
  showInstallPrompt.value = false
  trackInstallEvent('dismissed_by_user')
  
  // 24小时后再次显示
  localStorage.setItem('pwa_install_dismissed', Date.now().toString())
}

// 检查是否应该显示安装提示
const shouldShowPrompt = () => {
  const dismissedTime = localStorage.getItem('pwa_install_dismissed')
  if (dismissedTime) {
    const timeDiff = Date.now() - parseInt(dismissedTime)
    const oneDayInMs = 24 * 60 * 60 * 1000
    return timeDiff > oneDayInMs
  }
  return true
}

// 统计安装事件
const trackInstallEvent = (action: string) => {
  // 这里可以添加你的统计代码
  console.log('PWA Install Event:', action)
  
  // 示例：发送到Google Analytics
  if (typeof gtag !== 'undefined') {
    gtag('event', 'pwa_install', {
      event_category: 'PWA',
      event_label: action,
      value: 1
    })
  }
}

// 处理应用安装成功事件
const handleAppInstalled = () => {
  console.log('PWA安装成功')
  showInstallPrompt.value = false
  trackInstallEvent('installed')
}

onMounted(() => {
  // 检查基本条件
  if (!isPWASupported() || !isAndroid() || !isChrome() || isAppInstalled()) {
    return
  }
  
  // 检查是否应该显示提示
  if (!shouldShowPrompt()) {
    return
  }
  
  // 监听安装提示事件
  window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  
  // 监听安装成功事件
  window.addEventListener('appinstalled', handleAppInstalled)
})

onUnmounted(() => {
  window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  window.removeEventListener('appinstalled', handleAppInstalled)
})
</script>

<style scoped>
.pwa-install-prompt {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 9999;
  padding: 16px;
  animation: slideUp 0.3s ease-out;
}

.prompt-content {
  background: white;
  border-radius: 12px;
  padding: 20px;
  max-width: 400px;
  margin: 0 auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.prompt-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.app-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 12px;
}

.app-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.app-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.close-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 32px;
  height: 32px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.prompt-actions {
  display: flex;
  gap: 12px;
}

.install-btn {
  flex: 1;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.install-btn:hover {
  background: #0056CC;
}

.install-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.cancel-btn {
  flex: 1;
  background: transparent;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn:hover {
  background: #f5f5f5;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .pwa-install-prompt {
    padding: 12px;
  }
  
  .prompt-content {
    padding: 16px;
  }
  
  .prompt-actions {
    flex-direction: column;
  }
}
</style>
