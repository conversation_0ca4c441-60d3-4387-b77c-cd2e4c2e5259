<template>
  <div v-if="shouldShowInstaller" class="universal-pwa-installer">
    <div class="installer-content">
      <!-- 标准PWA安装提示 -->
      <div v-if="hasNativeSupport" class="native-installer">
        <div class="installer-header">
          <img src="/ic_launcher1.png" alt="App Icon" class="app-icon">
          <div class="app-info">
            <h3>安装 {{ appName }}</h3>
            <p>获得更好的体验，像原生应用一样使用</p>
          </div>
          <button @click="dismissInstaller" class="close-btn">×</button>
        </div>
        
        <div class="installer-actions">
          <button @click="installApp" class="install-btn" :disabled="installing">
            {{ installing ? '安装中...' : '立即安装' }}
          </button>
          <button @click="dismissInstaller" class="cancel-btn">稍后再说</button>
        </div>
      </div>

      <!-- 手动安装指导 -->
      <div v-else class="manual-installer">
        <div class="installer-header">
          <img src="/ic_launcher1.png" alt="App Icon" class="app-icon">
          <div class="app-info">
            <h3>安装 {{ appName }}</h3>
            <p>{{ getBrowserInstallGuide().description }}</p>
          </div>
          <button @click="dismissInstaller" class="close-btn">×</button>
        </div>

        <div class="install-steps">
          <div class="step-title">安装步骤：</div>
          <ol class="steps-list">
            <li v-for="(step, index) in getBrowserInstallGuide().steps" :key="index">
              {{ step }}
            </li>
          </ol>
        </div>

        <div class="installer-actions">
          <button @click="showInstallGuide" class="guide-btn">
            查看详细指导
          </button>
          <button @click="dismissInstaller" class="cancel-btn">稍后再说</button>
        </div>
      </div>
    </div>

    <!-- 详细安装指导弹窗 -->
    <div v-if="showGuideModal" class="guide-modal" @click="closeGuideModal">
      <div class="guide-content" @click.stop>
        <div class="guide-header">
          <h3>{{ getBrowserInstallGuide().title }}</h3>
          <button @click="closeGuideModal" class="close-btn">×</button>
        </div>
        
        <div class="guide-body">
          <div class="browser-info">
            <div class="browser-icon">{{ getBrowserIcon() }}</div>
            <div class="browser-name">{{ getBrowserName() }}</div>
          </div>
          
          <div class="detailed-steps">
            <div v-for="(step, index) in getBrowserInstallGuide().detailedSteps" :key="index" class="step-item">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <div class="step-text">{{ step.text }}</div>
                <div v-if="step.image" class="step-image">
                  <img :src="step.image" :alt="`步骤 ${index + 1}`">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

const appName = ref('cagadoslot')
const installing = ref(false)
const showGuideModal = ref(false)
const shouldShowInstaller = ref(false)
const hasNativeSupport = ref(false)
let deferredPrompt: BeforeInstallPromptEvent | null = null

// 浏览器检测函数
const getBrowserInfo = () => {
  const userAgent = navigator.userAgent
  
  if (/Chrome/i.test(userAgent) && !/Edge/i.test(userAgent)) {
    return { name: 'Chrome', type: 'chrome' }
  } else if (/SamsungBrowser/i.test(userAgent)) {
    return { name: 'Samsung Internet', type: 'samsung' }
  } else if (/Edge/i.test(userAgent)) {
    return { name: 'Microsoft Edge', type: 'edge' }
  } else if (/Firefox/i.test(userAgent)) {
    return { name: 'Firefox', type: 'firefox' }
  } else if (/Opera|OPR/i.test(userAgent)) {
    return { name: 'Opera', type: 'opera' }
  } else if (/UCBrowser/i.test(userAgent)) {
    return { name: 'UC Browser', type: 'uc' }
  } else if (/MiuiBrowser/i.test(userAgent)) {
    return { name: 'MIUI Browser', type: 'miui' }
  } else if (/HuaweiBrowser/i.test(userAgent)) {
    return { name: 'Huawei Browser', type: 'huawei' }
  } else if (/VivoBrowser/i.test(userAgent)) {
    return { name: 'Vivo Browser', type: 'vivo' }
  } else if (/OppoBrowser/i.test(userAgent)) {
    return { name: 'Oppo Browser', type: 'oppo' }
  } else {
    return { name: '当前浏览器', type: 'unknown' }
  }
}

const getBrowserName = () => getBrowserInfo().name
const getBrowserIcon = () => {
  const type = getBrowserInfo().type
  const icons: Record<string, string> = {
    chrome: '🌐',
    samsung: '🌍',
    edge: '🔷',
    firefox: '🦊',
    opera: '🎭',
    uc: '📱',
    miui: '📲',
    huawei: '📱',
    vivo: '📱',
    oppo: '📱',
    unknown: '🌐'
  }
  return icons[type] || '🌐'
}

// 获取浏览器特定的安装指导
const getBrowserInstallGuide = () => {
  const browserType = getBrowserInfo().type
  const browserName = getBrowserInfo().name
  
  const guides: Record<string, any> = {
    chrome: {
      title: 'Chrome 浏览器安装指导',
      description: '支持一键安装，体验最佳',
      steps: [
        '点击浏览器菜单（右上角三个点）',
        '选择"安装应用"或"添加到主屏幕"',
        '确认安装'
      ],
      detailedSteps: [
        { text: '点击浏览器右上角的菜单按钮（三个竖点）' },
        { text: '在菜单中找到"安装应用"或"添加到主屏幕"选项' },
        { text: '点击安装，应用将添加到您的桌面' }
      ]
    },
    samsung: {
      title: 'Samsung Internet 安装指导',
      description: '三星浏览器完美支持PWA安装',
      steps: [
        '点击底部菜单按钮',
        '选择"添加页面到"',
        '选择"主屏幕"'
      ],
      detailedSteps: [
        { text: '点击浏览器底部的菜单按钮' },
        { text: '选择"添加页面到"选项' },
        { text: '选择"主屏幕"完成安装' }
      ]
    },
    edge: {
      title: 'Microsoft Edge 安装指导',
      description: 'Edge浏览器支持PWA应用安装',
      steps: [
        '点击浏览器菜单（三个点）',
        '选择"应用"',
        '点击"将此站点安装为应用"'
      ],
      detailedSteps: [
        { text: '点击浏览器右上角的菜单按钮' },
        { text: '在菜单中找到"应用"选项' },
        { text: '点击"将此站点安装为应用"' }
      ]
    },
    firefox: {
      title: 'Firefox 浏览器安装指导',
      description: 'Firefox支持添加到主屏幕',
      steps: [
        '点击浏览器菜单',
        '选择"安装"或"添加到主屏幕"',
        '确认安装'
      ],
      detailedSteps: [
        { text: '点击浏览器菜单按钮' },
        { text: '查找"安装"或"添加到主屏幕"选项' },
        { text: '确认安装到桌面' }
      ]
    },
    opera: {
      title: 'Opera 浏览器安装指导',
      description: 'Opera支持PWA应用安装',
      steps: [
        '点击Opera菜单',
        '选择"主屏幕"',
        '点击"添加到主屏幕"'
      ],
      detailedSteps: [
        { text: '点击Opera浏览器菜单' },
        { text: '找到"主屏幕"相关选项' },
        { text: '选择"添加到主屏幕"' }
      ]
    },
    default: {
      title: `${browserName} 安装指导`,
      description: '大多数现代浏览器都支持PWA安装',
      steps: [
        '查找浏览器菜单中的"安装"选项',
        '或寻找"添加到主屏幕"功能',
        '按照提示完成安装'
      ],
      detailedSteps: [
        { text: '在浏览器菜单中查找"安装应用"、"添加到主屏幕"等选项' },
        { text: '如果没有找到，可以尝试在地址栏或分享菜单中查找' },
        { text: '按照浏览器提示完成安装过程' }
      ]
    }
  }
  
  return guides[browserType] || guides.default
}

// 检测设备和浏览器支持
const isAndroid = () => /Android/i.test(navigator.userAgent)
const isSupported = () => 'serviceWorker' in navigator
const isInstalled = () => {
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone === true
}

// 安装应用
const installApp = async () => {
  if (!deferredPrompt) {
    showInstallGuide()
    return
  }
  
  installing.value = true
  
  try {
    await deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice
    
    if (outcome === 'accepted') {
      console.log('用户接受了安装')
    }
    
    shouldShowInstaller.value = false
    deferredPrompt = null
  } catch (error) {
    console.error('安装失败:', error)
  } finally {
    installing.value = false
  }
}

// 显示安装指导
const showInstallGuide = () => {
  showGuideModal.value = true
}

// 关闭指导弹窗
const closeGuideModal = () => {
  showGuideModal.value = false
}

// 关闭安装器
const dismissInstaller = () => {
  shouldShowInstaller.value = false
  localStorage.setItem('pwa_install_dismissed', Date.now().toString())
}

// 检查是否应该显示
const shouldShow = () => {
  if (!isAndroid() || !isSupported() || isInstalled()) {
    return false
  }
  
  const dismissedTime = localStorage.getItem('pwa_install_dismissed')
  if (dismissedTime) {
    const timeDiff = Date.now() - parseInt(dismissedTime)
    const oneDayInMs = 24 * 60 * 60 * 1000
    return timeDiff > oneDayInMs
  }
  
  return true
}

// 处理beforeinstallprompt事件
const handleBeforeInstallPrompt = (e: Event) => {
  e.preventDefault()
  deferredPrompt = e as BeforeInstallPromptEvent
  hasNativeSupport.value = true
  
  if (shouldShow()) {
    shouldShowInstaller.value = true
  }
}

// 处理安装完成
const handleAppInstalled = () => {
  shouldShowInstaller.value = false
  console.log('应用安装成功')
}

onMounted(() => {
  if (shouldShow()) {
    // 延迟显示，给原生提示事件一些时间
    setTimeout(() => {
      if (!hasNativeSupport.value) {
        shouldShowInstaller.value = true
      }
    }, 3000)
  }
  
  window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  window.addEventListener('appinstalled', handleAppInstalled)
})

onUnmounted(() => {
  window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  window.removeEventListener('appinstalled', handleAppInstalled)
})
</script>

<style scoped>
.universal-pwa-installer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 9999;
  padding: 16px;
  animation: slideUp 0.3s ease-out;
}

.installer-content {
  background: white;
  border-radius: 12px;
  padding: 20px;
  max-width: 400px;
  margin: 0 auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.installer-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.app-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 12px;
}

.app-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.app-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.close-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 32px;
  height: 32px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.installer-actions {
  display: flex;
  gap: 12px;
}

.install-btn, .guide-btn {
  flex: 1;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.install-btn:hover, .guide-btn:hover {
  background: #0056CC;
}

.install-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.cancel-btn {
  flex: 1;
  background: transparent;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn:hover {
  background: #f5f5f5;
}

.install-steps {
  margin: 16px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.step-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.steps-list {
  margin: 0;
  padding-left: 20px;
}

.steps-list li {
  margin-bottom: 4px;
  color: #666;
  line-height: 1.4;
}

.guide-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
}

.guide-content {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  width: 100%;
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
}

.guide-header h3 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.guide-body {
  padding: 0 20px 20px;
}

.browser-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.browser-icon {
  font-size: 32px;
  margin-right: 12px;
}

.browser-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.detailed-steps {
  space-y: 16px;
}

.step-item {
  display: flex;
  margin-bottom: 16px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #007AFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 16px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-text {
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.step-image img {
  max-width: 100%;
  border-radius: 8px;
  border: 1px solid #ddd;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@media (max-width: 480px) {
  .universal-pwa-installer {
    padding: 12px;
  }
  
  .installer-content {
    padding: 16px;
  }
  
  .installer-actions {
    flex-direction: column;
  }
  
  .guide-modal {
    padding: 10px;
  }
}
</style>
