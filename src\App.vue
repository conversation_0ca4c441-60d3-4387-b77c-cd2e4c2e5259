<script setup lang="ts">
import AppActivePopup from "./components/AppActivePopup.vue";
import AdicionarPix from "../src/pages/takeout/AdicionarPix.vue";
import AppActivePopupB from "./components/AppActivePopupB.vue";
const route = useRoute();
const router = useRouter();
const dragStore = useDragStore();
const { showChatDrag } = storeToRefs(dragStore);

const appStore = useAppStore();
const { custService, isApp, activeDialogVisble } = storeToRefs(appStore);

const isDoNotShowToday = getSettingForToday("doNotShowToday");
// 检索 首充弹窗单日隐藏
function getSettingForToday(key: string) {
  const stored = localStorage.getItem(key);
  if (stored) {
    const { value, expiry } = JSON.parse(stored);
    const now = new Date();
    // console.log("现在时间", now.getTime())
    // console.log("对比时间", expiry)
    if (now.getTime() < expiry) {
      return value;
    }
    // 如果过期，清除设置
    localStorage.removeItem(key);
  }
  return null;
}

//弹出广告
const isLoginOut = localStorage.getItem("logonOut");
if (isLoginOut != "0") {
  appStore.runActivePopupData();
}
localStorage.setItem("logonOut", "1");

toggleTheme("blue");

const openService = (url?: string) => {
  // if (!url) return
  // window.open(url, '_blank')
  router.push("/promotion-detail/invitation-rewards");
};
const downloadUrl = ref("");
const openAndroid = (url?: string) => {
  if (!url) return;
  window.open(url, "_blank");
};

const openGoldCollect = () => {
  // const goldCollect = router.currentRoute.value.fullPath
  // if( !goldCollect.includes('goldCollect')){
  // router.push('/weekly-loss-cashback/goldCollect')
  router.push("/promotion-detail/recharge-rewards");
  // }else{
  //   // console.log("已经在这个界面不需要跳转")
  // }
};

watch(route, (val) => {
  if (
    val.path === "/" ||
    val.path === "/subgame/recente" ||
    val.path === "/subgame/favoritos"
  ) {
    dragStore.setShowChatDrag(true);
  } else {
    dragStore.setShowChatDrag(false);
  }
});

//监听如果大厅界面刷新
document.addEventListener("visibilitychange", (e) => {
  console.log(document.visibilityState);
  if (document.visibilityState === "visible") {
    const goldCollect = router.currentRoute.value.fullPath;
    if (goldCollect == "/finance") {
      console.log("刷新商城数据");
      // appStore.runGetMemberInfo()
      appStore.setIsRefreshShop(true);
      // router.go(0)
    }
  }
});

//----------------------浏览器处理-----------------------------------------------
const { mobile } = getBrowser();
const isShowIframe = ref(false);
const info = ref("");
if (mobile == "pc") {
  // 获取屏幕宽度
  const screenWidth = window.screen.width;
  // 获取屏幕高度
  const screenHeight = window.screen.height;
  if (screenWidth > screenHeight) {
  }
  info.value = window.location.href;
  let enterGame = GetQueryString("PC");
  if (enterGame == "") {
    let search = window.location.search;
    if (search == "") {
      info.value = window.location.href + "?PC=" + 1;
    } else {
      info.value = window.location.href + "&PC=" + 1;
    }
    isShowIframe.value = true;
  }
}

//----------------------浏览器处理-----------------------------------------------

// function isMobileDevice() {
//     let userAgent = navigator.userAgent || navigator.vendor || window.opera;
//     return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
// }

// if (isMobileDevice()) {
//     console.log("手机浏览器"); // 手机
//     document.documentElement.style.fontFamily = '-apple-system, pingfangsc-regular, pingfangsc-medium, "Microsoft YaHei", "微软雅黑","Helvetica Neue", Helvetica, "Hiragino Sans GB", Arial, sans-serif';
// } else {
//     console.log("电脑浏览器"); // 电脑
//     document.documentElement.style.fontFamily = 'Arial,"Microsoft YaHei", "微软雅黑", "Helvetica Neue", Helvetica, "Hiragino Sans GB", Arial, sans-serif';
// }
</script>

<template>
  <div v-if="isShowIframe" class="iframe_div">
    <iframe
      class="iframe_style"
      :style="{ width: `500px` }"
      :src="info"
      allowfullscreen="true"
    ></iframe>
  </div>
  <div v-else class="app-container">
    <!-- <AppActivePopup /> -->

    <AppActivePopupB />
    <AppFirstChargePopup
      v-if="!activeDialogVisble && isDoNotShowToday != 'true'"
    />
    <AppBaixar v-if="!isApp && showChatDrag" />
    <AppSplash />
    <Transition name="scale">
      <!-- <RouterView /> -->
      <AppHome/>
    </Transition>

    <AppLeftMenu />
    <AppPayRecordsPopup />
    <AppLoginRegister />
    <AppFindPassword />
    <AppFindPayPassword />
    <AdicionarPix />
    <AppFooter />

    <AppAgent />
    <AppPayPopup />
    <AppPersonalCenter />
    <AppPromotion />
    <AppRouterViewPopup />
    <!-- <AppFooterNew /> -->
    <AppEditAvatar />
    <!--<AppGameItemPopup />点击小游戏 弹出进入界面-->
    <!-- <AppAndroidDrag iconName="/icons/btn_android.png" :ballY="220" @clike="() => openAndroid(downloadUrl)"
      v-if="!isApp && showChatDrag" /> -->
    <!-- <AppChatDrag
      iconName="/icons/ActiveImg3511614282972711.gif"
      :ballY="140"
      @clike="() => openService(custService)"
      v-if="!isApp && showChatDrag"
    />-->

    <!-- <AppGoldCollect
      iconName="/icons/ActiveIm.gif"
      :ballY="60"
      @clike="() => openGoldCollect()"
      v-if="!isApp && showChatDrag"
    />  -->
    <AppRightActive v-if="!isApp && showChatDrag" />

    <!-- <AppMusic/> -->
    <!-- <AppIosInfo /> -->
    <AppBonusMessage />
    <GoldCollectPop />
    <AppLoading_all />
    <AppRecharge />
    <AppLogoutPrpup />
    <AppPrivacyPopup />
  </div>
</template>
<style>
html,
body {
  overscroll-behavior: none;
}
</style>
<style scoped>
.app-container {
  background-image: url(/img/index/main_bg.webp);
  background-size: cover;
  background-repeat: repeat;
  /* background: var(--theme-main-bg-color); */
  /* font-family: "MyAppFont"; */
  overflow: hidden !important;
  scrollbar-width: none;
  width: 750px;
  /* display: flex; */
  display: flex;
  flex-direction: column;
  height: calc(1vh * 100);
}

.iframe_div {
  background-color: var(--theme-bg-color);
  /* background: url("/icons/h_bg.webp") center center;
  background-size: cover;
  background-size: 100% 100%; */
}

.iframe_style {
  display: block;
  margin: 0 auto;
  height: 100vh;
}
</style>
