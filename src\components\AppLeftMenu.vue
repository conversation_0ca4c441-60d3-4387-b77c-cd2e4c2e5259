<script setup lang="ts">
import { GameNavEnum, CycleModeEnum, GameHallTopEnum } from "~/types/common";

const appStore = useAppStore();
const { isShowAppBaixar } = storeToRefs(appStore);
const gameStore = useGameStore();
const router = useRouter();
const route = useRoute();
const { id } = route.params;
const clickId = ref(+id);

const {
  showLeftMenu,
  curCycleMode,
  isPlayingMusic,
  iosInfoShow,
  secletType,
  showMusicPlayer,
} = storeToRefs(appStore);

const menus = [
  { path: "/", id: GameHallTopEnum.Popular, cateName: "Popular" },
  { path: "/subgame", id: GameHallTopEnum.Solts, cateName: "Slots" },
  {
    path: "/subgame/recente",
    id: GameHallTopEnum.Recente,
    cateName: "Recente",
  },
  {
    path: "/subgame/favoritos",
    id: GameHallTopEnum.Favoritos,
    cateName: "Favoritos",
  },
];

const liClick = (item: any) => {
  console.log(JSON.stringify(item));
  clickId.value = item.id;
  showLeftMenu.value = false;
  if (item.path) {
    router.push(item.path);
    return;
  }
  router.push(`/game-list/${item.id}`);
};

watch(
  route,
  (val, old) => {
    if (val.path === "/") {
      clickId.value = GameHallTopEnum.Popular;
      return;
    }
    if (val.path === "/subgame/recente") {
      clickId.value = GameHallTopEnum.Recente;
      return;
    }
    if (val.path === "/subgame/favoritos") {
      clickId.value = GameHallTopEnum.Favoritos;
      return;
    }

    // if (val.path === '/Slots'){
    //   clickId.value = GameNavEnum.Slots
    //   return
    // }
    // if (val.path.indexOf('/game-list/') === -1) {
    //   clickId.value = -1
    // } else {
    //   clickId.value = +route.params.id
    // }
  },
  { immediate: true }
);

// const showMusicPlayer = ref(false);
const audioPlayer = ref(null);
const progress = ref(0);
const duration = ref(0);

const currentTime = ref(0);
const durationTime = ref("00:00");
const audioSrc = ref("");
const curMusicIndex = ref(0);
const curMusicId = ref(1);
const curMyMusicIndex = ref(0);
let myMusicArray = [1];
const IsOnlyOne = ref(true);

function setMusicData() {
  // localStorage.removeItem('myMusicArray');
  const storedArrayString = localStorage.getItem("DownloadMusicArray");
  if (storedArrayString) {
    const storedArray = JSON.parse(storedArrayString);
    musicNum.value = storedArray.length;
    myMusicArray.splice(0);
    musicData1.value.splice(0);
    for (let i = 0; i < storedArray.length; i++) {
      let item = musicData.value.find(
        (item) => item.id == Number(storedArray[i])
      );
      item.state = MUSIC_STATE.Downloaded;
      myMusicArray.push(Number(storedArray[i]));
      musicData1.value.push(item);
    }
  } else {
    musicData.value[0].state = MUSIC_STATE.Downloaded;
    musicData1.value.push(musicData.value[0]);
  }
  audioSrc.value =
    brazilImg +
    "/music/" +
    musicData1.value[0].name +
    musicData1.value[0].format;
  curMusicId.value = musicData1.value[0].id;
  curMusicIndex.value = musicData1.value[0].id - 1;

  IsOnlyOne.value = false;
  if (myMusicArray.length == 1) {
    IsOnlyOne.value = true;
  }
  console.log("setMusicData");
}

function togglePlay() {
  isPlayingMusic.value = !isPlayingMusic.value; // 切换播放状态
  // 这里可以添加实际控制音乐播放的逻辑
  if (isPlayingMusic.value == true) {
    if (audioPlayer.value) {
      audioPlayer.value.play();
    }
  } else {
    if (audioPlayer.value) {
      audioPlayer.value.pause();
    }
  }
}

const onChange = (e) => {
  audioPlayer.value.currentTime =
    (audioPlayer.value.duration * progress.value) / 100;
};

const updateProgress = () => {
  if (audioPlayer.value.duration) {
    const newTime = audioPlayer.value.currentTime;
    currentTime.value = newTime.toFixed(2);
    progress.value = (newTime / audioPlayer.value.duration) * 100;
  }
};

const updateTime = () => {
  if (audioPlayer.value) {
    duration.value = audioPlayer.value.duration;
    durationTime.value = formatTime(duration.value);
    if (isPlayingMusic.value == true) {
      audioPlayer.value.play();
    }
  }
};

onMounted(() => {
  setMusicData();
  setCurSongName();
  audioPlayer.value = new Audio(audioSrc.value);
  audioPlayer.value.load();
  audioPlayer.value.pause();
  audioPlayer.value.addEventListener("timeupdate", updateProgress);
  audioPlayer.value.addEventListener("loadedmetadata", updateTime);
});

// 格式化时间函数
const formatTime = (time) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;
};

function previousSong() {
  // 添加逻辑以播放前一首歌曲
  if (curCycleMode.value == 1 || curCycleMode.value == 3) {
    curMyMusicIndex.value =
      (curMyMusicIndex.value - 1 + musicData1.value.length) %
      musicData1.value.length;
  } else if (curCycleMode.value == 2) {
    getRandomMusicIndex();
  }
  curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
  curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
  loadSong(musicData1.value[curMyMusicIndex.value]);
  setCurSongName();
}

function nextSong() {
  // 添加逻辑以播放下一首歌曲
  if (curCycleMode.value == 1 || curCycleMode.value == 3) {
    curMyMusicIndex.value =
      (curMyMusicIndex.value + 1) % musicData1.value.length;
  } else if (curCycleMode.value == 2) {
    getRandomMusicIndex();
  }
  curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
  curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
  loadSong(musicData1.value[curMyMusicIndex.value]);
  setCurSongName();
}

const playNextSong = () => {
  if (curCycleMode.value == 1) {
    curMyMusicIndex.value =
      (curMyMusicIndex.value + 1) % musicData1.value.length;
  } else if (curCycleMode.value == 2) {
    getRandomMusicIndex();
  }
  curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
  curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
  loadSong(musicData1.value[curMyMusicIndex.value]);
  setCurSongName();
};

function getRandomMusicIndex() {
  if (musicData1.value.length == 1) return;
  let randomIndex;
  do {
    randomIndex = Math.floor(Math.random() * musicData1.value.length);
  } while (randomIndex === curMyMusicIndex.value);
  curMyMusicIndex.value = randomIndex;
}

function musicItemClick(index, state) {
  if (state == MUSIC_STATE.Downloaded) {
    isPlayingMusic.value = true;

    if (index != curMusicIndex.value) {
      curMusicIndex.value = index;
      curMusicId.value = musicData.value[curMusicIndex.value].id;
      curMyMusicIndex.value = musicData1.value.findIndex(
        (item) => item.id == curMusicId.value
      );
      loadSong(musicData.value[curMusicIndex.value]);
      setCurSongName();
    } else {
      if (isPlayingMusic.value == true) {
        if (audioPlayer.value) {
          audioPlayer.value.play();
        }
      } else {
        if (audioPlayer.value) {
          audioPlayer.value.pause();
        }
      }
    }
  } else {
    handleDownloadClick(index);
  }
}

const downloadTime = ref([3000, 3500, 4000, 4500, 5000]);

const handleDownloadClick = (index) => {
  const item = musicData.value[index];
  let time = downloadTime.value[Math.floor(Math.random() * 5)];
  // console.log("downloadTime", time)
  if (item) {
    item.isLoading = true;

    // 模拟下载过程
    setTimeout(() => {
      // 下载完成
      item.isLoading = false;
      item.state = MUSIC_STATE.Downloaded;
      // alert('下载' + item.name + '完成');
      myMusicArray.push(item.id);
      musicData1.value.push(item);
      console.log(myMusicArray, musicData1);

      saveMyMusicArray();
      //下载完播放
      myMusicItemClick(musicData1.value.length - 1);
    }, time); // 假设下载需要2秒
  }
};

function myMusicItemClick(index) {
  isPlayingMusic.value = true;

  if (index != curMyMusicIndex.value) {
    curMyMusicIndex.value = index;
    curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
    curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
    loadSong(musicData1.value[curMyMusicIndex.value]);
    setCurSongName();
  } else {
    if (isPlayingMusic.value == true) {
      if (audioPlayer.value) {
        audioPlayer.value.play();
      }
    } else {
      if (audioPlayer.value) {
        audioPlayer.value.pause();
      }
    }
  }
}

function deleteSong(deleteIndex) {
  if (IsOnlyOne.value) return;
  if (deleteIndex == curMyMusicIndex.value) {
    myMusicArray.splice(deleteIndex, 1);
    musicData1.value[deleteIndex].state = MUSIC_STATE.Not_Downloaded;
    musicData1.value.splice(deleteIndex, 1);
    console.log(myMusicArray, musicData1, musicData);
    saveMyMusicArray();

    if (musicData1.value.length > 0) {
      curMyMusicIndex.value = 0;
      curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
      curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
      loadSong(musicData1.value[curMyMusicIndex.value]);
      setCurSongName();
    }
  } else {
    myMusicArray.splice(deleteIndex, 1);
    musicData1.value[deleteIndex].state = MUSIC_STATE.Not_Downloaded;
    musicData1.value.splice(deleteIndex, 1);
    console.log(myMusicArray, musicData1, musicData);
    saveMyMusicArray();
    //删完歌之后重新设置index
    curMyMusicIndex.value = musicData1.value.findIndex(
      (item) => item.id == curMusicId.value
    );
  }
}

function saveMyMusicArray() {
  // 将数组转换为 JSON 字符串
  const arrayString = JSON.stringify(myMusicArray);
  // 存储字符串到 LocalStorage
  localStorage.setItem("DownloadMusicArray", arrayString);

  musicNum.value = myMusicArray.length;

  IsOnlyOne.value = false;
  if (myMusicArray.length == 1) {
    IsOnlyOne.value = true;
  }
}

const loadSong = (song) => {
  audioPlayer.value.src = brazilImg + "/music/" + song.name + song.format;
  console.log(audioPlayer.value.src);
  audioPlayer.value.load();
  duration.value = audioPlayer.value.duration;
  currentTime.value = 0;
  progress.value = 0;
  // if (isPlayingMusic.value == true) {
  //   if (audioPlayer.value) {
  //     audioPlayer.value.play();
  //   }
  // }
};

function switchCycleMode() {
  if (curCycleMode.value < 3) {
    curCycleMode.value++;
  } else {
    curCycleMode.value = 1;
  }
}

function getCycleModeName(mode: number) {
  let name = "";
  switch (mode) {
    case CycleModeEnum.Sequential_Loop:
      name = "Ciclo";
      break;
    case CycleModeEnum.Random_Cycle:
      name = "Aleatório";
      break;
    case CycleModeEnum.Single_Loop:
      name = "Repetir";
      break;
  }
  return name;
}

function openMusicList() {
  showMusicPlayer.value = !showMusicPlayer.value;
}

const songName = ref("");

function setCurSongName() {
  songName.value = musicData1.value[curMyMusicIndex.value].name;
}

function truncatedText(text: string, length: number) {
  return text.length > length ? text.substring(0, length) + "..." : text;
}

const isHoveringDownload = ref(false); // 用于追踪鼠标是否悬停在按钮上
const isHoveringSupport = ref(false); // 用于追踪鼠标是否悬停在按钮上
const isHoveringHelp = ref(false); // 用于追踪鼠标是否悬停在按钮上

const musicNum = ref(1);

const SECLET_TYPE = readonly({
  System_Music: "0",
  My_Music: "1",
});

const MUSIC_STATE = readonly({
  Not_Downloaded: 0,
  Downloaded: 1,
});
const musicSecletType = ref(SECLET_TYPE.System_Music);

const tabData = ref([
  {
    label: "Sistema de Musica",
    value: SECLET_TYPE.System_Music,
  },
  {
    label: "Minhas musicas",
    value: SECLET_TYPE.My_Music,
  },
]);

const musicData = ref([
  {
    id: 1,
    name: "You Spin Me Round",
    size: "2M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 2,
    name: "Mariah Carey - Without You",
    size: "2M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 3,
    name: "Nicky Jam _ Will Smith _ Era Istrefi - Live It Up (Official Song 2018 FIFA World Cup Russia)",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 4,
    name: "Sexy Love",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 5,
    name: "Shakira - 6.Waka Waka",
    size: "2M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 6,
    name: "Shakira - 7.Try Everything (From Zootopia) [Official Music Video]",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 7,
    name: "Shakira - 8.Whenever, Wherever (Official Music Video)",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 8,
    name: "Shape of You",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 9,
    name: "Sia-53.Chandelier",
    size: "937K",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 10,
    name: "Sia-54.Cheap Thrills",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 11,
    name: "Sia-55.Dusk Till Dawn",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 12,
    name: "Sia-55.Move Your Body",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 13,
    name: "Silver Scrapes",
    size: "2M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 14,
    name: "Skin",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 15,
    name: "Someone Like You",
    size: "",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 16,
    name: "Something Just Like This",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 17,
    name: "Soviet March",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 18,
    name: "Taylor Swift - 103.Blank Space",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 19,
    name: "Taylor Swift - 104.You Belong With Me",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 20,
    name: "Taylor Swift - 105.Shake It Off",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 21,
    name: "Taylor Swift - 106.Bad Blood ft. Kendrick Lamar",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 22,
    name: "That Girl",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 23,
    name: "The Chainsmokers、Coldplay - 15.Something Just Like This",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 24,
    name: "The Chainsmokers-67.Closer",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 25,
    name: "The Chainsmokers-68.Don_t Let Me Down",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 26,
    name: "The Chainsmokers-69.Paris",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 27,
    name: "The Days",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 28,
    name: "The Fox",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 29,
    name: "The Nights(Remix)",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 30,
    name: "The Phoenix",
    size: "937K",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 31,
    name: "Titanium",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 32,
    name: "Toni Braxton - Yesterday",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 33,
    name: "Victory",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 34,
    name: "Wait Wait Wait",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 35,
    name: "Waiting for Love",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 36,
    name: "Will Smith-9.Live It Up",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 37,
    name: "Wiz Khalifa _ Charlie Puth - See You Again",
    size: "2M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
]);

const musicData1 = ref([]);

const onTabChange = () => {
  if (musicSecletType.value == SECLET_TYPE.System_Music) {
    // scrollToSystemItem(curMusicIndex.value);
  } else if (musicSecletType.value == SECLET_TYPE.My_Music) {
    // scrollToMyItem(curMyMusicIndex.value);
  }
};

function downloadMusic(state: number) {
  return;
  showToast(state);
}

const scrollContainer = ref(null);
const systemRefs = ref([]);
const myRefs = ref([]);
const scrollToSystemItem = (index) => {
  if (!scrollContainer.value || !systemRefs.value[index]) return;
  showToast(index);

  scrollContainer.value.scrollTo({
    top: systemRefs.value[index].offsetTop,
    behavior: "smooth",
  });
};

const scrollToMyItem = (index) => {
  if (!scrollContainer.value || !myRefs.value[index]) return;
  showToast(index);

  scrollContainer.value.scrollTo({
    top: myRefs.value[index].offsetTop,
    behavior: "smooth",
  });
};

const enum JumpViewType {
  PROMOTION = 0,
  JUROS,
  VIP,
  REBATE,
  PENDENTE,
  HISTORY,
}

function jumpFunction(type: JumpViewType) {
  showLeftMenu.value = false;

  if (type == JumpViewType.PROMOTION) {
    secletType.value = 0;
    router.push("/advancement");
  } else if (type == JumpViewType.REBATE) {
    router.push({ path: "/advancement", query: { key: type } });
  } else if (type == JumpViewType.PENDENTE) {
    router.push({ path: "/advancement", query: { key: type } });
  } else if (type == JumpViewType.HISTORY) {
    router.push({ path: "/advancement", query: { key: type } });
  } else if (type == JumpViewType.JUROS) {
    router.push({ path: "/advancement", query: { key: type } });
  } else if (type == JumpViewType.VIP) {
    router.push({ path: "/advancement", query: { key: type } });
  }
}

const { run: runGetPlatformLinkData, data: platformLinkData } = useRequest(
  () => ApiGetPlatformLinkData(),
  {
    manual: true,
    onSuccess(res: any) {
      // console.log(res)
    },
  }
);
runGetPlatformLinkData();

const jumpUrl = (url: string) => {
  window.open(url, "_blank");
};

const enum JumpViewTypeVip {
  PROMOTION = 0,
  JUROS,
  VIP,
  REBATE,
  PENDENTE,
  HISTORY,
}

const goPage = (url: string) => {
  showLeftMenu.value = false;
  appStore.setShowRouterView(true);
  if (url == "vip") {
    console.log(url);
    router.push({ path: "/advancement", query: { key: JumpViewTypeVip.VIP } });
    return;
  } else if (url == "Pendente") {
    router.push({
      path: "/advancement",
      query: { key: JumpViewTypeVip.PENDENTE },
    });
  } else if (url == "rebate") {
    router.push({
      path: "/advancement",
      query: { key: JumpViewTypeVip.REBATE },
    });
  } else if (url == "agent") {
    router.push("/agent");
  } else if (url == "Poupança") {
    router.push({
      path: "/advancement",
      query: { key: JumpViewTypeVip.JUROS },
    });
  } else if (url == "Histórico") {
    router.push({
      path: "/advancement",
      query: { key: JumpViewTypeVip.HISTORY },
    });
  } else if (url == "Eventos") {
    router.push({
      path: "/advancement",
      query: { key: JumpViewTypeVip.PROMOTION },
    });
  } else if (url == "Apostas") {
    router.push("/report");
  } else if (url == "dias") {
    router.push("/attendance");
  }

  return;
  router.push(url);
  appStore.setShowRouterView(true);
};
</script>

<template>
  <van-popup
    class="left"
    v-model:show="showLeftMenu"
    duration:0.3
    position="left"
    teleport="body"
    z-index="1001"
    :overlay-style="{
      transition: 'margin-top 0.5s ease 0s',
    }"
    :style="{
      width: '70%',
      height: '100%',
      transition: 'margin-top 0.5s ease 0s',
      paddingBottom: isShowAppBaixar ? 'var(--app-px-158)' : 'var(--app-px-88)',
    }"
  >
    <!-- {{ setMusicData() }}
      marginTop: isShowAppBaixar ? 'var(--app-px-158)' : 'var(--app-px-88)',
      marginTop: isShowAppBaixar ? 'var(--app-px-158)' : 'var(--app-px-88)',
    {{ setCurSongName() }}  -->
    <div class="top-bar">
      <div @click="appStore.setLeftMenuState(false)" class="goback">
        <img src="/img/leftMenu/navv2_icon_back.png" />
      </div>

      <img class="logo" src="/logo1.png.webp" />
    </div>

    <!-- <section class="game-menu">
      <div v-for="(item, index) in menus" :key="item.id" :name="item.id">
        <div
          class="game-item"
          :class="{ active: clickId === item.id }"
          @click="liClick(item)"
        >
          <AppImage
            class="icon"
            :class="`icon_${10000}`"
            :src="`/icons/nav_${item.id}${
              clickId === item.id ? '-active' : ''
            }.png`"
            alt=""
          />
          <div class="text">{{ item.cateName }}</div>
        </div>
      </div>
    </section> -->
    <section class="aside-sys-menu">
      <!-- <div class="aside-music">
        <div class="headerMusicPlayer">
          <div class="control-buttons">
            <audio
              ref="audioPlayer"
              :src="audioSrc"
              @timeupdate="updateProgress"
              @loadedmetadata="updateTime"
              @ended="playNextSong"
            />
            <AppImage
              class="next"
              src="/img/leftMenu/music_previous_song.webp"
              alt=""
              @click="previousSong"
            />
            <AppImage
              :src="
                isPlayingMusic
                  ? '/img/leftMenu/music_pause.webp'
                  : '/img/leftMenu/music_play.webp'
              "
              alt=""
              @click="togglePlay"
            />
            <AppImage
              class="next"
              src="/img/leftMenu/music_next_song.webp"
              alt=""
              @click="nextSong"
            />
            <AppImage
              :src="`/img/leftMenu/music_cycle_mode${curCycleMode}.webp`"
              alt=""
              @click="switchCycleMode"
            />
            <div class="control-buttons-list" @click="openMusicList">
              <div class="music-number">
                <span> {{ musicNum }} </span>
              </div>
              <AppImage src="/img/leftMenu/music_number.webp" />
            </div>
          </div>
          <span class="songName">{{ truncatedText(songName, 28) }}</span>
        </div>
      </div> -->
      <!-- <div
        class="betting-record"
        @click="
          () => {
            router.push('/report');
            showLeftMenu = false;
          }
        "
      >
        <AppImage src="/img/leftMenu/betting_record_icon.webp" alt="" />
        <span>Apostas</span>
      </div>
      <div
        class="betting-record"
        @click="
          () => {
            router.push('/agent');
            showLeftMenu = false;
          }
        "
      >
        <AppImage src="/img/leftMenu/agent_icon.webp" alt="" />
        <span>Agente</span>
      </div> -->
      <div class="activity">
        <div class="activity-item" @click="goPage('vip')">
          <img src="/img/leftMenu/vip_reward.png" /><span>VIP</span>
        </div>
        <div class="activity-item" @click="goPage('Pendente')">
          <img src="/img/leftMenu/box.png" /><span>Pendente</span>
        </div>
        <div class="activity-item" @click="goPage('dias')">
          <img src="/img/leftMenu/7day.png" /><span>7 dias</span>
        </div>
        <div class="activity-item" @click="goPage('agent')">
          <img src="/img/leftMenu/agent_commission.png" /><span>Agente</span>
        </div>
        <!-- <div class="activity-item" @click="goPage()">
          <img src="/img/leftMenu/user_rank.png" /><span>Nível</span>
        </div> -->
        <div class="activity-item" @click="goPage('Eventos')">
          <img src="/img/leftMenu/mystery.png" /><span>Eventos</span>
        </div>
        <!-- <div class="activity-item" @click="goPage()">
          <img src="/img/leftMenu/red_envelope_rain.png" /><span>01:28:43</span>
        </div> -->
        <div class="activity-item" @click="goPage('Poupança')">
          <img src="/img/leftMenu/recharge_bargain_sales.png" /><span
            >Poupança</span
          >
        </div>
        <!-- <div class="activity-item" @click="goPage()">
          <img src="/img/leftMenu/member_day.png" /><span>Super</span>
        </div> -->
        <div class="activity-item" @click="goPage('Apostas')">
          <img src="/img/leftMenu/game_benefit.png" /><span>Apostas</span>
        </div>
        <!-- <div class="activity-item" @click="goPage()">
          <img src="/img/leftMenu/week_benefit.png" /><span>Semana</span>
        </div> -->
        <!-- <div class="activity-item" @click="goPage()">
          <img src="/img/leftMenu/pinduoduo.png" /><span>Magnata</span>
        </div> -->
        <div class="activity-item" @click="goPage('Histórico')">
          <img src="/img/leftMenu/redeem_code.png" /><span>Histórico</span>
        </div>
        <div class="activity-item" @click="goPage('rebate')">
          <img src="/img/leftMenu/global_wash_chip.png" /><span>Rebate</span>
        </div>
        <!-- <div class="title">
          <span>Promoção</span>
        </div>
        <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_1.webp"
            alt=""
            @click="jumpFunction(JumpViewType.PROMOTION)"
          />
          <span>Eventos</span>
        </div> -->
        <!-- <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_2.webp"
            alt=""
            @click="jumpFunction(JumpViewType.REBATE)"
          />
          <span>Rebate</span>
        </div> -->
        <!-- <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_3.webp"
            alt=""
            @click="jumpFunction(JumpViewType.PENDENTE)"
          />
          <span>Pendente</span>
        </div>
        <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_4.webp"
            alt=""
            @click="jumpFunction(JumpViewType.HISTORY)"
          />
          <span>Histórico</span>
        </div> -->
        <!-- <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_5.webp"
            alt=""
            @click="jumpFunction(JumpViewType.JUROS)"
          />
          <span>Poupança</span>
          <AppImage
            class="activity-item-tip"
            src="/img/leftMenu/qipao0.webp"
            alt=""
          />
          <div class="activity-item-tip-text">500,00%</div>
        </div> -->
        <!-- <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_6.webp"
            alt=""
            @click="jumpFunction(JumpViewType.VIP)"
          />
          <span>VIP</span>
        </div> -->
        <!-- <AppImage class="activity-item" src="/img/leftMenu/activity_1.webp" alt="" @click="jumpFunction(JumpViewType.PROMOTION)"/>
        <AppImage class="activity-item" src="/img/leftMenu/activity_2.webp" alt="" @click="jumpFunction(JumpViewType.REBATE)"/>
        <AppImage class="activity-item" src="/img/leftMenu/activity_3.webp" alt="" @click="jumpFunction(JumpViewType.PENDENTE)"/>
        <AppImage class="activity-item" src="/img/leftMenu/activity_4.webp" alt="" @click="jumpFunction(JumpViewType.HISTORY)"/> -->
        <!-- <div class="activity-item">
          <AppImage class="activity-item" src="/img/leftMenu/activity_5_1.webp" alt="" @click="jumpFunction(JumpViewType.JUROS)"/>
          <AppImage class="activity-item-tip" src="/img/leftMenu/qipao0.webp" alt="" />
          <div class="activity-item-tip-text">500,00%</div>
        </div> -->

        <!-- <AppImage class="activity-item" src="/img/leftMenu/activity_5.webp" alt="" @click="jumpFunction(JumpViewType.JUROS)"/> -->
        <!-- <AppImage class="activity-item" src="/img/leftMenu/activity_6.webp" alt="" @click="jumpFunction(JumpViewType.VIP)"/> -->
        <!-- <AppImage class="activity-item1" src="/img/leftMenu/activity_7.webp" alt="" /> -->
      </div>
      <div class="other-group">
        <!-- 使用v-if判断当前状态来显示图片 -->
        <!-- <AppImage v-if="!isHoveringDownload" src="/img/leftMenu/menu_download_normal.webp" alt="Regular State" />
          <AppImage v-if="isHoveringDownload" src="/img/leftMenu/menu_download_select.webp" alt="Hover State" /> -->

        <img
          @click="
            () => {
              router.push('/serviceMessages');
              appStore.setShowRouterView(true);

              showLeftMenu = false;
            }
          "
          src="/img/leftMenu/homev2_menu_service_bg.png"
        />
      </div>
      <div class="offical">
        <!-- <img class="offical-bg" src="/img/leftMenu/homev2_menu_share_bg.png" /> -->

        <div class="offical-item" @click="jumpUrl(platformLinkData.instagram)">
          <img src="/img/leftMenu/home_share_18.png" />
        </div>
        <div class="offical-item" @click="jumpUrl(platformLinkData.instagram)">
          <img src="/img/leftMenu/home_share_facebook.png" />
        </div>

        <div
          class="offical-item"
          v-show="platformLinkData?.telegram.length > 0"
          @click="jumpUrl(platformLinkData.telegram)"
        >
          <img src="/img/leftMenu/home_share_telegram.png" />
        </div>

        <div class="offical-item" @click="jumpUrl(platformLinkData.instagram)">
          <img src="/img/leftMenu/home_share_insgram.png" />
        </div>

        <!-- v-show="platformLinkData?.instagram.length > 0" -->

        <!-- <div class="link">
          <AppImage v-show="platformLinkData?.telegram.length > 0" src="/icons/telegram.webp" @click="jumpUrl(platformLinkData.telegram)"/>
          <AppImage v-show="platformLinkData?.facebook.length > 0" src="/icons/facebook.webp" @click="jumpUrl(platformLinkData.facebook)"/>
          <AppImage v-show="platformLinkData?.twitter.length > 0" src="/icons/twitter.webp" @click="jumpUrl(platformLinkData.twitter)" />
          <AppImage v-show="platformLinkData?.instagram.length > 0" src="/icons/instagram.webp" @click="jumpUrl(platformLinkData.instagram)" />
        </div> -->
        <!-- <div class="offical-item">
          <AppImage src="/img/leftMenu/Instagram.webp" />
          <span>Instagram</span>
        </div>
        <div class="offical-item">
          <AppImage src="/img/leftMenu/Instagram.webp" />
          <span>Instagram</span>
        </div>
        <div class="offical-item">
          <AppImage src="/img/leftMenu/Instagram.webp" />
          <span>Instagram</span>
        </div> -->
      </div>
      <div class="empty" />
    </section>
  </van-popup>

  <van-popup class="music-player-poup" v-model:show="showMusicPlayer" round>
    <div class="music-player">
      <span>Música</span>
      <div class="music-control">
        <span class="name">{{ truncatedText(songName, 60) }}</span>
        <div class="slider-content">
          <span class="time">{{ formatTime(currentTime) }}</span>
          <van-slider
            class="slider"
            v-model="progress"
            bar-height="5px"
            inactive-color="var(--theme-text-color-placeholder)"
            active-color="var(--theme-primary-color)"
            button-size="15px"
            @change="onChange"
          />
          <span class="time1">{{ durationTime }}</span>
        </div>
        <div class="operate-content">
          <div class="cycle-mode" @click="switchCycleMode">
            <AppImage
              class="img"
              :src="`/img/leftMenu/music_cycle_mode${curCycleMode}.webp`"
              alt=""
            />
            <span>{{ getCycleModeName(curCycleMode) }}</span>
          </div>
          <AppImage
            class="img1"
            src="/img/musicPlayer/music_previous_song.webp"
            alt=""
            @click="previousSong"
          />
          <AppImage
            class="img2"
            :src="
              isPlayingMusic
                ? '/img/musicPlayer/music_pause.webp'
                : '/img/musicPlayer/music_play.webp'
            "
            alt=""
            @click="togglePlay"
          />
          <AppImage
            class="img1"
            src="/img/musicPlayer/music_next_song.webp"
            alt=""
            @click="nextSong"
          />
          <div class="music-num">
            <span :style="{ fontSize: '15px' }">{{ musicNum }}</span>
            <span>Baixado</span>
          </div>
        </div>
      </div>
      <div class="music-list">
        <div class="music-list-content">
          <AppTab
            class="music-list-tab"
            :list-data="tabData"
            v-model="musicSecletType"
            @change="onTabChange"
          ></AppTab>
          <!-- <AppImage src="/img/musicPlayer/line.webp" alt="" /> -->
        </div>
        <div class="content" ref="scrollContainer">
          <div
            v-if="musicSecletType == SECLET_TYPE.System_Music"
            class="content-system"
          >
            <div
              class="music-item"
              v-for="(data, index) in musicData"
              :key="index"
              ref="systemRefs"
              @click="musicItemClick(index, data.state)"
            >
              <div v-if="curMusicIndex == index" class="music-info">
                <AppImage
                  :style="{ width: '14px', height: '16px' }"
                  src="/img/musicPlayer/music_icon.webp"
                  alt=""
                />
                <span class="child-style-4">{{
                  truncatedText(data.name, 31)
                }}</span>
                <span class="child-style-5">{{ data.size }}</span>
              </div>
              <div v-else class="music-info">
                <span class="child-style-1">{{ index + 1 }}</span>
                <span class="child-style-2">{{
                  truncatedText(data.name, 31)
                }}</span>
                <span class="child-style-3">{{ data.size }}</span>
              </div>
              <div class="music-state">
                <!-- <span>{{ data.state }}</span> -->
                <div v-if="data.isLoading" class="loader" />
                <div v-else>
                  <div v-if="data.state == 0">
                    <AppImage
                      class="state1"
                      src="/img/musicPlayer/music_download.webp"
                      alt=""
                      @click="downloadMusic(data.state)"
                    />
                  </div>
                  <div v-else>
                    <AppImage
                      class="state2"
                      src="/img/musicPlayer/music_gou.webp"
                      alt=""
                    />
                  </div>
                </div>
              </div>
              <!-- <span> {{ data.name }}</span> -->
            </div>
          </div>
          <div
            v-else-if="musicSecletType == SECLET_TYPE.My_Music"
            class="content-my"
          >
            <div
              class="music-item"
              v-for="(data, index) in musicData1"
              :key="index"
              ref="myRefs"
              @click="myMusicItemClick(index)"
            >
              <div v-if="curMusicId == data.id" class="music-info">
                <AppImage
                  :style="{ width: '14px', height: '16px' }"
                  src="/img/musicPlayer/music_icon.webp"
                  alt=""
                />
                <span class="child-style-4">{{
                  truncatedText(data.name, 28)
                }}</span>
                <span class="child-style-5">{{ data.size }}</span>
              </div>
              <div v-else class="music-info">
                <span class="child-style-1">{{ index + 1 }}</span>
                <span class="child-style-2">{{
                  truncatedText(data.name, 28)
                }}</span>
                <span class="child-style-3">{{ data.size }}</span>
              </div>
              <div class="music-operation">
                <div class="music-state">
                  <AppImage
                    class="state2"
                    src="/img/musicPlayer/music_gou.webp"
                    alt=""
                  />
                </div>
                <div class="delete">
                  <AppImage
                    :style="{ width: '15px', height: '15px' }"
                    src="/img/musicPlayer/music_delete.webp"
                    alt=""
                    @click.stop="() => deleteSong(index)"
                  />
                </div>
              </div>
              <!-- <span> {{ data.name }}</span> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <AppImage
      class="close-btn"
      src="/img/musicPlayer/music_close.webp"
      alt=""
      @click="
        () => {
          showMusicPlayer = false;
        }
      "
    />
  </van-popup>
</template>

<!-- <style lang="scss">
[theme='blue']:root {
  
}
</style> -->
<style lang="scss" scoped>
@import "../theme/mixin.scss";
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .logo {
    height: 8vw;
  }
}
.goback {
  img {
    width: 80px;
    height: auto;
  }
}

.left {
  padding-left: 3vw;
  padding-right: 3vw;
  // background-color: var(--theme-main-bg-color);
  background-image: linear-gradient(126deg, #d3ccfd 0%, #e7e4ff 100%);
  // var(--app-navbar-height)
  // margin-top: 20.95vw !important;
  padding-top: 30px;
  overflow: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  // transform: translateY(10%);
}

.left::-webkit-scrollbar {
  display: none;
}

.game-menu {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  position: relative;
  // margin-top: 6px;

  .game-item {
    // @include webp('/img/leftMenu/item_unselecte');
    background-color: var(--theme-side-menu-btn-color);
    border-radius: 14px;
    background-size: 136px 96px;
    margin-left: 10px;
    margin-top: 10px;
    width: 136px;
    height: 96px;
    position: relative;
    text-align: center;
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;
    padding-top: 10px;

    .text {
      position: relative;
      width: 130px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 18px;
      color: var(--app-slots-text-color);
      color: var(--theme-side-menu-text-color);
      padding-top: 4px;
    }

    // &.active {
    //   // @include webp('/img/leftMenu/item_selecte');
    //   background-color: var(--theme-primary-color);
    //   .text {
    //     color: var(--theme-primary-font-color);
    //   }
    // }

    .icon_10000 {
      width: 60px;
      position: relative;
      // transform: translate(-50%,-50%);
      // left:50%;
      // top:50%;
      margin: 0 auto;
    }

    .icon_0 {
      top: 0px;
      width: 46px;
      height: 46px;
      position: relative;
    }

    .icon_1 {
      top: 0px;
      width: 42px;
      height: 42px;
      position: relative;
    }

    .icon_2 {
      top: 0px;
      width: 42px;
      height: 42px;
      position: relative;
    }

    .icon_3 {
      top: 0px;
      width: 44px;
      height: 44px;
      position: relative;
    }

    .icon_7 {
      top: 0px;
      width: 42px;
      height: 42px;
      position: relative;
    }

    .icon_8 {
      top: 0px;
      width: 44px;
      height: 44px;
      position: relative;
    }

    .icon_100 {
      top: 0px;
      width: 44px;
      height: 44px;
      position: relative;
    }
  }
}

.aside-sys-menu {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  // margin-top: 10px;
  flex-direction: column;

  .aside-music {
    width: 280px;
    height: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .headerMusicPlayer {
      // @include webp('/img/leftMenu/music_player_bg');
      background-color: var(--theme-side-menu-btn-color);
      border-radius: 10px;
      width: 280px;
      height: 76px;
      background-size: 280px 76px;
      display: flex;
      flex-direction: column;
      position: relative;
      align-items: center;

      // justify-content: center;
      .control-buttons {
        left: -10px;
        width: 100%;
        padding-top: 8px;
        margin-top: 5px;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        img {
          position: relative;
          margin-left: 24px;
          width: 30px;
          /* 根据需要调整 */
          // height: 30px;
          /* 根据需要调整 */
        }

        &-list {
          margin-left: 30px;
          @include webp("/img/leftMenu/music_list");
          background-size: 32px 26px;
          width: 32px;
          height: 26px;

          .music-number {
            @include webp("/img/leftMenu/music_number");
            background-size: 29px 21px;
            width: 29px;
            height: 21px;
            text-align: center;
            display: flex;
            justify-content: center;
            /* 水平居中 */
            align-items: center;
            position: absolute;
            top: -6px;
            left: 212px;
          }

          span {
            color: var(--theme-text-color);
            font-size: 16px;
          }
        }
        .next {
          width: 20px;
          // height: 20px;
        }
      }

      // .control-buttons img {
      //   position: relative;
      //   margin-left: 20px;
      //   width: 30px;
      //   /* 根据需要调整 */
      //   height: 30px;
      //   /* 根据需要调整 */
      // }

      .songName {
        color: var(--theme-side-menu-text-color);
        font-size: 18px;
      }
    }
  }

  .betting-record {
    // @include webp('/img/leftMenu/betting_record_bg');
    background-color: var(--theme-side-menu-btn-color);
    border-radius: 10px;
    width: 280px;
    height: 80px;
    background-size: 280px 80px;
    display: flex;
    flex-direction: row;
    position: relative;
    align-items: center;
    margin-top: 10px;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding-left: 20px;

    img {
      width: 35px;
      height: 36px;
      position: relative;
      // left:-100px;
    }

    span {
      width: 190px;
      color: var(--theme-side-menu-text-color);
      font-size: 24px;
    }
  }

  .activity {
    // @include webp('/img/leftMenu/activity_bg');
    // background-color: var(--theme-side-menu-btn-color);
    border-radius: 10px;
    width: 100%;
    height: 67vw;
    background-size: 280px 340px;
    margin-top: 40px;
    margin-bottom: 30px;
    padding-top: 10px;
    display: grid;
    grid-template-columns: repeat(3, 1fr); // 新增：一行三个
    gap: 10px; // 新增：图片间距
    // justify-content: flex-start;
    // flex-wrap: wrap;
    // position: relative;

    .title {
      width: 280px;
      height: 32px;
      text-align: center;
      span {
        color: var(--theme-side-menu-text-color);
        font-size: 24px;
      }
    }
    .activity-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      margin-bottom: 24px;
    }
    .activity-item img {
      width: 80px;
      height: 80px;
      object-fit: contain;
      margin: 0 auto 10px auto;
      display: block;
    }
    .activity-item-tip {
      position: absolute;
      left: 53px;
      top: -16px;
      width: 83px;
      height: 26px;
    }
    .activity-item-tip-text {
      position: absolute;
      left: 53px;
      top: -16px;
      width: 83px;
      height: 26px;
      color: rgb(255, 255, 0);
      font-size: 18px;
    }
    .activity-item1 {
      margin-left: 10px;
      margin-right: 1px;
      margin-top: 0px;
      position: relative;
      text-align: center;
      display: flex;
      justify-content: center;
      /* 水平居中 */
      align-items: center;
      /* 垂直居中 */
      flex-direction: column;
      width: 260px;
      height: 82px;
    }
  }
  .activity img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin: 0 auto;
    display: block;
  }

  .other-group {
    width: 100%;

    // padding-top: 10px;
    // padding-left: 20px;
    img {
      width: 100%;
      // height: 60px;
    }

    .other-item {
      width: 270px;
      height: 60px;
      display: flex;
      align-items: center;

      div {
        display: flex;
        align-items: center;
      }

      .icon {
        width: 30px;
        height: 30px;
        margin-right: 15px; // Fixed margin for all icons
      }

      .other-item-text {
        color: black;
        font-size: 20px;
        color: var(--theme-side-menu-text-color);
      }

      img {
        width: 100%;
        // height: 60px;
      }
    }
  }

  .offical {
    width: 100%;
    margin-top: 20px;
    height: 22vw;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .offical-bg {
      width: 100%;
      height: auto;
    }

    padding: 0 50px;

    background-image: url("/img/leftMenu/homev2_menu_share_bg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    span {
      color: var(--theme-text-color-lighten);
      font-size: 24px;
      padding-left: 6px;
    }

    .offical-item {
      // @include webp('/img/leftMenu/offical_bg');
      // border-radius:20px;
      // background-color: var(--theme-side-menu-btn-color);
      border-radius: 10px;
      background-size: 270px 52px;
      background-repeat: no-repeat;
      width: 270px;
      height: 52px;
      margin-top: 12px;
      display: flex;
      // justify-content: center;
      /* 水平居中 */
      align-items: center;
      justify-content: center;
      /* 垂直居中 */
      // flex-direction: row;

      padding-left: 16px;

      img {
        width: auto;
        height: 50px;
      }

      span {
        color: var(--theme-text-color-darken);
        font-size: 20px;
        font-weight: 400;
      }
    }
  }

  .empty {
    width: 280px;
    height: 30px;
  }
}

.music-player-poup {
  width: 690px;
  height: 1200px;
  display: flex;
  // align-items: center;
  // flex-direction: column;
  position: absolute;
  justify-content: center;

  .close-btn {
    position: absolute;
    width: 56px;
    height: 56px;
    top: 95%;
  }
}

.music-player {
  width: 680px;
  height: 980px;
  background-color: var(--theme-bg-color);
  background-size: 690px 980px;
  text-align: center;
  position: absolute;
  display: flex;
  // justify-content: top;
  align-items: center;
  /* 垂直居中 */
  flex-direction: column;
  padding-top: 20px;
  border-radius: 20px;
  border: 2px solid var(--theme-color-line);
  top: 8%;

  span {
    position: relative;
    color: var(--theme-text-color-darken);
    font-size: 28px;
  }

  .music-control {
    margin-top: 20px;
    position: relative;
    width: 588px;
    height: 224px;
    background-color: var(--theme-main-bg-color);
    border-radius: 16px;
    display: flex;
    justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;

    .name {
      padding-top: 22px;
      position: relative;
      color: var(--theme-text-color-darken);
      font-size: 21px;
    }

    .slider-content {
      margin-top: 26px;
      // padding-top: 40px;
      position: relative;
      width: 588px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      /* 垂直居中 */
      flex-direction: row;

      .slider {
        width: 300px;
      }

      .time {
        padding-right: 50px;
        color: var(--theme-text-color);
        font-size: 20px;
      }

      .time1 {
        padding-left: 50px;
        color: var(--theme-text-color);
        font-size: 20px;
      }
    }

    .operate-content {
      margin-top: 26px;
      position: relative;
      width: 588px;
      height: 80px;
      display: flex;
      flex-direction: row;
      /* 水平布局 */
      justify-content: space-around;
      /* 子节点之间的间距平均分布 */
      align-items: center;

      /* 垂直居中对齐 */
      .cycle-mode {
        width: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
        /* 垂直居中 */
        flex-direction: column;

        span {
          font-size: 22px;
          top: 0px;
        }
      }

      .music-num {
        display: flex;
        justify-content: center;
        align-items: center;
        /* 垂直居中 */
        flex-direction: column;

        span {
          color: var(--theme-text-color-lighten);
          font-size: 22px;
        }
      }

      .img {
        width: 32px;
        // height: 28px;
      }

      .img1 {
        width: 56px;
        height: 56px;
      }

      .img2 {
        width: 76px;
        height: 76px;
      }
    }
  }

  .music-list {
    margin-top: 20px;
    position: relative;
    width: 588px;
    height: 610px;
    background-color: var(--theme-main-bg-color);
    border-radius: 16px;
    display: flex;
    justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;

    .music-list-content {
      width: 588px;
      height: 40px;

      .music-list-tab {
        width: 588px;

        position: absolute;
        top: 0px;
        border-bottom: thin solid var(--theme-color-line);
      }

      img {
        position: absolute;
        width: 588px;
        height: 2px;
        top: 69px;
        left: 0px;
      }
    }

    .content {
      // padding-top: 36px;
      margin-top: 36px;
      width: 588px;
      height: 524px;
      position: relative;
      display: flex;
      justify-content: top;
      align-items: center;
      /* 垂直居中 */
      flex-direction: column;
      overflow-y: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;

      .content::-webkit-scrollbar {
        display: none;
      }

      .content-system {
        // width: 588px;
        // height: 524px;
        width: 100%;
        position: relative;
        display: flex;
        justify-content: top;
        align-items: center;
        /* 垂直居中 */
        flex-direction: column;

        .music-item {
          box-sizing: border-box;
          width: 560px;
          height: 80px;
          position: relative;
          list-style-type: none;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: thin solid var(--theme-color-line);
          /* 子节点之间的分隔线 */
          // margin-top: 30px;
          // margin-bottom: 30px;

          .music-info,
          .music-state {
            display: flex;
            align-items: center;
          }

          .music-info {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-direction: row;

            img {
              margin-left: 10px;
            }

            .child-style-1 {
              padding-left: 10px;
              /* 子节点1样式 */
              color: var(--theme-text-color-lighten);
              font-size: 22px;
            }

            .child-style-2 {
              padding-left: 10px;
              /* 子节点2样式 */
              color: var(--theme-text-color-darken);
              font-size: 24px;
            }

            .child-style-3 {
              padding-left: 10px;
              /* 子节点3样式 */
              color: var(--theme-text-color-lighten);
              font-size: 20px;
            }

            .child-style-4 {
              padding-left: 12px;
              /* 子节点3样式 */
              color: var(--theme-primary-color);
              font-size: 24px;
            }

            .child-style-5 {
              padding-left: 12px;
              /* 子节点3样式 */
              color: var(--theme-text-color-lighten);
              font-size: 22px;
            }
          }

          .music-state {
            justify-content: flex-end;

            .state1 {
              width: 32px;
              height: 32px;
            }

            .state2 {
              width: 30px;
              height: 22px;
            }

            .loader {
              /* 加载指示器样式 */
              border: 4px solid #f3f3f3;
              border-top: 4px solid #3498db;
              border-radius: 50%;
              width: 20px;
              height: 20px;
              animation: spin 2s linear infinite;
              margin-right: 5px;
            }

            @keyframes spin {
              0% {
                transform: rotate(0deg);
              }

              100% {
                transform: rotate(360deg);
              }
            }
          }
        }
      }

      .content-my {
        // width: 588px;
        // height: 524px;
        width: 100%;
        position: relative;
        display: flex;
        justify-content: top;
        align-items: center;
        /* 垂直居中 */
        flex-direction: column;

        .music-item {
          box-sizing: border-box;
          width: 560px;
          height: 80px;
          position: relative;
          list-style-type: none;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: thin solid var(--theme-color-line);
          /* 子节点之间的分隔线 */
          // margin-top: 30px;
          // margin-bottom: 30px;

          .music-info,
          .music-operation {
            display: flex;
            align-items: center;
          }

          .music-info {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-direction: row;

            img {
              margin-left: 10px;
            }

            .child-style-1 {
              padding-left: 10px;
              /* 子节点1样式 */
              color: var(--theme-text-color-lighten);
              font-size: 22px;
            }

            .child-style-2 {
              padding-left: 10px;
              /* 子节点2样式 */
              color: var(--theme-text-color-darken);
              font-size: 24px;
            }

            .child-style-3 {
              padding-left: 10px;
              /* 子节点3样式 */
              color: var(--theme-text-color-lighten);
              font-size: 20px;
            }

            .child-style-4 {
              padding-left: 12px;
              /* 子节点3样式 */
              color: var(--theme-primary-color);
              font-size: 24px;
            }

            .child-style-5 {
              padding-left: 12px;
              /* 子节点3样式 */
              color: var(--theme-text-color-lighten);
              font-size: 22px;
            }
          }

          .music-operation {
            img {
              margin-right: 10px;
              margin-left: 6px;
            }

            .music-state {
              justify-content: flex-end;

              .state1 {
                width: 32px;
                height: 32px;
              }

              .state2 {
                width: 30px;
                height: 22px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
